--[[AddEventHandler('esx:playerLoaded', function(source)
	local _source        = source
	local xPlayer        = ESX.GetPlayerFromId(_source)

	MySQL.Async.fetchAll('SELECT `xp` FROM users WHERE identifier = @identifier', {
		['@identifier'] = xPlayer.identifier
	}, function(result)

			local xpbd = {}

			if result[1].xp ~= nil then
				xpbd = json.decode(result[1].xp)
			end
			   Wait(0)
    TriggerClientEvent("Clark_XpSystem:AddXpIfPlayerSpwanInServerSide", _source , xpbd)
	TriggerClientEvent("Clark_XpSystem:ShowBar", _source)

		end
	)

end)

RegisterNetEvent('Clark_XpSystem:ServerControl')
AddEventHandler('Clark_XpSystem:ServerControl',function(data, value, token)
	local _source = source
    -- if not exports['esx_misc2']:secureServerEvent(GetCurrentResourceName(), _source, token) then
    --     return false
    -- end
	local xPlayer  = ESX.GetPlayerFromId(_source)
	MySQL.Async.fetchAll('SELECT `xp` FROM users WHERE identifier = @identifier', {
		['@identifier'] = xPlayer.identifier
	}, function(result)
        -- print(result[1].rank)
       local xpbd = result[1].xp
   
		if data == 'add' then
			MySQL.Async.fetchAll("UPDATE users SET xp = @xp WHERE identifier = @identifier", { ['@identifier'] = xPlayer.identifier, ['@xp'] = xpbd + (value)})
		elseif data == 'remove' then
			if xpbd >= value then
				MySQL.Async.fetchAll("UPDATE users SET xp = @xp WHERE identifier = @identifier", { ['@identifier'] = xPlayer.identifier, ['@xp'] = xpbd - (value)})
			else
				MySQL.Async.fetchAll("UPDATE users SET xp = @xp WHERE identifier = @identifier", { ['@identifier'] = xPlayer.identifier, ['@xp'] = 0})
			end
		end
	end)
end)

RegisterNetEvent('Mody:server:DubleXP')
AddEventHandler('Mody:server:DubleXP', function()
	TriggerClientEvent('Mody:DubleXP', -1)
end)


ESX.RegisterServerCallback('rp-radio:checkradioitem', function(source, cb)
	local _source = source
	local xPlayer = ESX.GetPlayerFromId(_source)
	if xPlayer then
		if xPlayer.getInventoryItem('radio').count >= 1 then
			cb(true)
		else
			cb(false)
		end
	else
		cb(false)
	end
end)]]
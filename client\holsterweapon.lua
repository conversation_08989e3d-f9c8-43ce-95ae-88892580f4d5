----------------------------------------------------------------
-- Copyright © 2019 by <PERSON>
-- Made By: Guy293
-- GitHub: https://github.com/Guy293
-- Fivem Forum: https://forum.fivem.net/u/guy293/
-- Tweaked by: <PERSON><PERSON><PERSON><PERSON> & <PERSON>
----------------------------------------------------------------

--- DO NOT EDIT THIS --
holstered  = true
blocked	 = false
------------------------

--[[  Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		local sleep = true
		loadAnimDict("rcmjosh4")
		loadAnimDict("reaction@intimidation@cop@unarmed")
		loadAnimDict("reaction@intimidation@1h")
		local ped = PlayerPedId()

		if not IsPedInAnyVehicle(ped, false) then
			sleep = false
			if GetVehiclePedIsTryingToEnter (ped) == 0 and (GetPedParachuteState(ped) == -1 or GetPedParachuteState(ped) == 0) and not IsPedInParachuteFreeFall(ped) then
				if CheckWeapon(ped) then
					--if IsPedArmed(ped, 4) then
					if holstered then
						blocked   = true
							SetPedCurrentWeaponVisible(ped, 0, 1, 1, 1)
							TaskPlayAnim(ped, "reaction@intimidation@cop@unarmed", "intro", 8.0, 2.0, -1, 50, 2.0, 0, 0, 0 ) -- Change 50 to 30 if you want to stand still when removing weapon
							--TaskPlayAnim(ped, "reaction@intimidation@cop@unarmed", "intro", 8.0, 2.0, -1, 30, 2.0, 0, 0, 0 ) Use this line if you want to stand still when removing weapon
							if leojob then
								Config.cooldownCurrent = Config.cooldownPolice
							else
								Config.cooldownCurrent = Config.cooldownCurrent
							end
							Citizen.Wait(Config.cooldownCurrent)
							SetPedCurrentWeaponVisible(ped, 1, 1, 1, 1)
							TaskPlayAnim(ped, "rcmjosh4", "josh_leadout_cop2", 8.0, 2.0, -1, 48, 10, 0, 0, 0 )
								Citizen.Wait(400)
							ClearPedTasks(ped)
						holstered = false
					else
						blocked = false
					end
				else
				--elseif not IsPedArmed(ped, 4) then
					if not holstered then
				
							TaskPlayAnim(ped, "rcmjosh4", "josh_leadout_cop2", 8.0, 2.0, -1, 48, 10, 0, 0, 0 )
								Citizen.Wait(500)
							TaskPlayAnim(ped, "reaction@intimidation@cop@unarmed", "outro", 8.0, 2.0, -1, 50, 2.0, 0, 0, 0 ) -- Change 50 to 30 if you want to stand still when holstering weapon
							--TaskPlayAnim(ped, "reaction@intimidation@cop@unarmed", "outro", 8.0, 2.0, -1, 30, 2.0, 0, 0, 0 ) Use this line if you want to stand still when holstering weapon
								Citizen.Wait(60)
							ClearPedTasks(ped)
						holstered = true
					end
				end
			else
				SetCurrentPedWeapon(ped, GetHashKey("WEAPON_UNARMED"), true)
			end
		else
			holstered = true
		end

		if sleep then
		Citizen.Wait(500)
		end
	end
end)
 ]]
--[[
Citizen.CreateThread(function()
	while true do
		Citizen.Wait(0)
		local letsleep = true
		if blocked then
			letsleep = false
			DisableControlAction(1, 25, true )
			DisableControlAction(1, 140, true)
			DisableControlAction(1, 141, true)
			DisableControlAction(1, 142, true)
			DisableControlAction(1, 23, true)
			DisableControlAction(1, 37, true) -- Disables INPUT_SELECT_WEAPON (TAB)
			DisablePlayerFiring(ped, true) -- Disable weapon firing
		end

		if letsleep then
			Citizen.Wait(500)
		end
	end
end)


function CheckWeapon(ped)
	if IsEntityDead(ped) then
		blocked = false
			return false
		else
			for i = 1, #Config.Weapons do
				if GetHashKey(Config.Weapons[i]) == GetSelectedPedWeapon(ped) then
					return true
				end
			end
		return false
	end
end


function loadAnimDict(dict)
	while ( not HasAnimDictLoaded(dict)) do
		RequestAnimDict(dict)
		Citizen.Wait(0)
	end
end--]]

function fixAllWrongs()
	local playerPed = PlayerPedId()
	ClearPedSecondaryTask(playerPed)
	SetEnableHandcuffs(playerPed, false)
	DisablePlayerFiring(playerPed, false)
	SetPedCanPlayGestureAnims(playerPed, true)
	FreezeEntityPosition(playerPed, false)
	DetachEntity(PlayerPedId(), true, false)
	DisplayRadar(true)
	RemoveLoadingPrompt()
	
	SendNUIMessage(false)
	
	disableAllControlActions = false
	TriggerEvent('esx_atm:closeATM') -- يغلق الصرافة
	TriggerEvent('abdulrhman:esx_animations2:holsterweapon:fix_blocked') -- يصلح مشكلة ركوب السيارة و إخراج السلاح
	TriggerEvent('esx_misc:hidehud', false) -- اذا كان فيه مشكلة في الهود يصلحها
	TriggerEvent('wk:clearScreen') -- رادار الشرطة
	TriggerEvent('esx_headbag:removeBag') -- إزالة الخيشة

	ESX.UI.Menu.CloseAll()
end

--  خذ التريقر هذا وحطه بمجلد wk_wars2x اذا كنت تستخدمه، أو غير التريقر للمطلوب
-- RegisterNetEvent( "wk:clearScreen" )
-- AddEventHandler( "wk:clearScreen", function()
-- 	SetNuiFocus( false, false )
-- 	SendNUIMessage( { _type = "close" } )
-- 	if ( RADAR:IsMenuOpen() ) then
-- 		RADAR:CloseMenu()
-- 	end
-- 	SYNC:SetRemoteOpenState( false )

-- end)

local count = 0
	
	local function lagCooldown(sec)
		CreateThread(function()
			count = sec
			while count ~= 0 do
				count = count - 1
				Wait(1000)
			end	
			count = 0
		end)	
	end

RegisterCommand('lag', function()
	if exports['esx_misc']:ishandcuffedmisc() then  return end
    if count == 0 then 
	blocked = false
	fixAllWrongs()
	lagCooldown(60)
	PlaySoundFrontend(source, "OTHER_TEXT", "HUD_AWARDS", true)
	ESX.ShowNotification('في حال عدم حل المشكلة إفصل و ارجع من السيرفر')
	else
	ESX.ShowNotification('<font color=red>يجب الأنتظار.</font> <font color=orange>'..count..' ثانية')
	end
end, false)

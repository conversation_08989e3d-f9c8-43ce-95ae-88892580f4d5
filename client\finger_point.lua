local mp_pointing = false
local keyPressed = false

local function startPointing()
    local ped = PlayerPedId()
    RequestAnimDict("anim@mp_point")
    while not HasAnimDictLoaded("anim@mp_point") do
        Wait(0)
    end
    SetPedCurrentWeaponVisible(ped, 0, 1, 1, 1)
    SetPedConfigFlag(ped, 36, 1)
    Citizen.InvokeNative(0x2D537BA194896636, ped, "task_mp_pointing", 0.5, 0, "anim@mp_point", 24)
    RemoveAnimDict("anim@mp_point")
end

local function stopPointing()
    local ped = PlayerPedId()
    Citizen.InvokeNative(0xD01015C7316AE176, ped, "Stop")
    if not IsPedInjured(ped) then
        ClearPedSecondaryTask(ped)
    end
    if not IsPedInAnyVehicle(ped, 1) then
        SetPedCurrentWeaponVisible(ped, 1, 1, 1, 1)
    end
    SetPedConfigFlag(ped, 36, 0)
    ClearPedSecondaryTask(PlayerPedId())
end

local once = true
local oldval = false
local oldvalped = false

-- Optimized Finger Pointing System
Citizen.CreateThread(function()
    local playerPed = PlayerPedId()
    local lastPedUpdate = 0
    local lastPointingCheck = 0

    while true do
        local sleep = 100 -- Base sleep increased from 0 to 100ms
        local currentTime = GetGameTimer()

        -- Update cached playerPed every 5 seconds
        if currentTime - lastPedUpdate > 5000 then
            playerPed = PlayerPedId()
            lastPedUpdate = currentTime
        end

        if once then
            once = false
        end

        if not keyPressed then
            if IsControlPressed(0, 29) and not mp_pointing and IsPedOnFoot(playerPed) then
                sleep = 10 -- Reduce sleep when actively checking controls
                Wait(200)
                if not IsControlPressed(0, 29) then
                    keyPressed = true
                    startPointing()
                    mp_pointing = true
                else
                    keyPressed = true
                    while IsControlPressed(0, 29) do
                        Wait(50)
                    end
                end
            elseif (IsControlPressed(0, 29) and mp_pointing) or (not IsPedOnFoot(playerPed) and mp_pointing) then
                keyPressed = true
                mp_pointing = false
                stopPointing()
                sleep = 10
            end
        end

        if keyPressed then
            sleep = 50 -- Moderate sleep when key is pressed
            if not IsControlPressed(0, 29) then
                keyPressed = false
            end
        end

        -- Optimize pointing checks - only check every 50ms when pointing
        if currentTime - lastPointingCheck > 50 then
            if Citizen.InvokeNative(0x921CE12C489C4C41, playerPed) and not mp_pointing then
                stopPointing()
            end

            if Citizen.InvokeNative(0x921CE12C489C4C41, playerPed) then
                if not IsPedOnFoot(playerPed) then
                    stopPointing()
                else
                    local camPitch = GetGameplayCamRelativePitch()
                    if camPitch < -70.0 then
                        camPitch = -70.0
                    elseif camPitch > 42.0 then
                        camPitch = 42.0
                    end
                    camPitch = (camPitch + 70.0) / 112.0

                    local camHeading = GetGameplayCamRelativeHeading()
                    local cosCamHeading = Cos(camHeading)
                    local sinCamHeading = Sin(camHeading)
                    if camHeading < -180.0 then
                        camHeading = -180.0
                    elseif camHeading > 180.0 then
                        camHeading = 180.0
                    end
                    camHeading = (camHeading + 180.0) / 360.0

                    local coords = GetOffsetFromEntityInWorldCoords(playerPed, (cosCamHeading * -0.2) - (sinCamHeading * (0.4 * camHeading + 0.3)), (sinCamHeading * -0.2) + (cosCamHeading * (0.4 * camHeading + 0.3)), 0.6)
                    local ray = Cast_3dRayPointToPoint(coords.x, coords.y, coords.z - 0.2, coords.x, coords.y, coords.z + 0.2, 0.4, 95, playerPed, 7);
                    local _, blocked = GetRaycastResult(ray)

                    Citizen.InvokeNative(0xD5BB4025AE449A4E, playerPed, "Pitch", camPitch)
                    Citizen.InvokeNative(0xD5BB4025AE449A4E, playerPed, "Heading", camHeading * -1.0 + 1.0)
                    Citizen.InvokeNative(0xB0A6CFD2C69C1088, playerPed, "isBlocked", blocked)
                    Citizen.InvokeNative(0xB0A6CFD2C69C1088, playerPed, "isFirstPerson", Citizen.InvokeNative(0xEE778F8C7E1142E2, Citizen.InvokeNative(0x19CAFA3C87F7C2FF)) == 4)
                end
                sleep = 10 -- Faster updates when actively pointing
            end
            lastPointingCheck = currentTime
        end

        Citizen.Wait(sleep)
    end
end)
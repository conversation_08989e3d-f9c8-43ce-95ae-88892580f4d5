-- Optimized Vehicle Steering Persistence
Citizen.CreateThread(function()
    local angle = 0.0
    local speed = 0.0
    local playerPed = PlayerPedId()
    local lastPedUpdate = 0

    while true do
        local sleep = 100 -- Base sleep time increased from 0 to 100ms
        local currentTime = GetGameTimer()

        -- Update cached playerPed every 5 seconds to handle respawning
        if currentTime - lastPedUpdate > 5000 then
            playerPed = PlayerPedId()
            lastPedUpdate = currentTime
        end

        local veh = GetVehiclePedIsUsing(playerPed)
        if DoesEntityExist(veh) then
            sleep = 50 -- Reduce to 50ms when in vehicle for better responsiveness
            local tangle = GetVehicleSteeringAngle(veh)
            if tangle > 10.0 or tangle < -10.0 then
                angle = tangle
            end
            speed = GetEntitySpeed(veh)

            -- Only check for steering persistence when vehicle is stopped
            if speed < 0.1 and not GetIsTaskActive(playerPed, 151) and not GetIsVehicleEngineRunning(veh) then
                SetVehicleSteeringAngle(veh, angle)
            end
        end

        Citizen.Wait(sleep)
    end
end)
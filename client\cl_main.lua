govjob = false
leojob = false
leojob2 = false
adminjob = false
ESXDATA = {}

local admin_blips = true
exports('GetAdminBlipsState', function()
  return admin_blips
end)

RegisterNetEvent('esx_misc:adminblips')
AddEventHandler('esx_misc:adminblips', function(state)
  admin_blips = state
end)


Citizen.CreateThread(function()

  while ESXDATA.job == nil do
		Citizen.Wait(500)
	end
  
  ESXDATA = ESX.GetPlayerData()
  Citizen.Wait(500)
  isLeooradminorgovJob()
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
	ESXDATA = xPlayer
  Citizen.Wait(500)
  isLeooradminorgovJob()
end)

RegisterNetEvent('esx:setJob')
AddEventHandler('esx:setJob', function(job)
	ESXDATA.job = job
  Citizen.Wait(500)
  isLeooradminorgovJob()
end)

RegisterCommand('car', function(source, args)
  if exports.leojob:GetAdminIsOnline() then
    local vehicleName = args[1]
    local model = (type(vehicleName) == 'number' and vehicleName or GetHashKey(vehicleName))

    if IsModelInCdimage(model) then
      local playerPed = PlayerPedId()
      local playerCoords, playerHeading = GetEntityCoords(playerPed), GetEntityHeading(playerPed)

      ESX.Game.SpawnVehicle(model, playerCoords, playerHeading, function(vehicle)
        TaskWarpPedIntoVehicle(playerPed, vehicle, -1)
      end)
    else
      TriggerEvent('chat:addMessage', {args = {'^1SYSTEM', 'Invalid vehicle model.'}})
    end
  end
end, false)

RegisterCommand('dv', function(source, args)

  if exports.leojob:GetAdminIsOnline() then

    local radius = args[1]
    local playerPed = PlayerPedId()

      if radius and tonumber(radius) then
        radius = tonumber(radius) + 0.01
        local vehicles = ESX.Game.GetVehiclesInArea(GetEntityCoords(playerPed), radius)

        for k,entity in ipairs(vehicles) do
          local attempt = 0

          while not NetworkHasControlOfEntity(entity) and attempt < 100 and DoesEntityExist(entity) do
            Citizen.Wait(100)
            NetworkRequestControlOfEntity(entity)
            attempt = attempt + 1
          end

          if DoesEntityExist(entity) and NetworkHasControlOfEntity(entity) then
            ESX.Game.DeleteVehicle(entity)
          end
        end
      else
        local vehicle, attempt = ESX.Game.GetVehicleInDirection(), 0

        if IsPedInAnyVehicle(playerPed, true) then
          vehicle = GetVehiclePedIsIn(playerPed, false)
        end

        while not NetworkHasControlOfEntity(vehicle) and attempt < 100 and DoesEntityExist(vehicle) do
          Citizen.Wait(100)
          NetworkRequestControlOfEntity(vehicle)
          attempt = attempt + 1
        end

        if DoesEntityExist(vehicle) and NetworkHasControlOfEntity(vehicle) then
          ESX.Game.DeleteVehicle(vehicle)
        end
      end
end

end, false)

AddEventHandler('playerSpawned', function()
  TriggerEvent('esx_misc:unrestrain')

	--START PVP Enabled--
	Citizen.CreateThread(function()
		local player 	= PlayerId()
		local playerPed = PlayerPedId()
		-- Enable pvp
		NetworkSetFriendlyFireOption(true)
		SetCanAttackFriendly(playerPed, true, true)

		-- إعدادات إضافية لمنع ظهور البوتات عند البداية
		SetMaxWantedLevel(0)
		SetPoliceIgnorePlayer(player, true)
		SetDispatchCopsForPlayer(player, false)
		SetPlayerCanUseCover(player, false)

		-- منع ظهور الشرطة والإسعاف
		for i = 1, 12 do
			EnableDispatchService(i, false)
		end

		-- منع ظهور البوتات في السيناريوهات
		SetScenarioTypeEnabled("WORLD_VEHICLE_ATTRACTOR", false)
		SetScenarioTypeEnabled("WORLD_VEHICLE_AMBULANCE", false)
		SetScenarioTypeEnabled("WORLD_VEHICLE_POLICE_NEXT_TO_CAR", false)
		SetScenarioTypeEnabled("WORLD_VEHICLE_POLICE_CAR", false)
		SetScenarioTypeEnabled("WORLD_VEHICLE_POLICE_BIKE", false)
	end)
end)

AddEventHandler('onResourceStop', function(resource)
	if resource == GetCurrentResourceName() then
		TriggerEvent('esx_misc:unrestrain')

		if HandcuffTimer and HandcuffTimer.Active then
			ESX.ClearTimeout(HandcuffTimer.Task)
		end
	end
end)

function isLeooradminorgovJob()
  while ESXDATA.job == nil do
		Citizen.Wait(10)
	end

  if ESXDATA.job.name == 'admin' then
    adminjob = true
  else
    adminjob = false
  end

  if ESXDATA.job.name == 'police' or ESXDATA.job.name == 'ambulance' or ESXDATA.job.name == 'agent' or ESXDATA.job.name == 'admin' then
    govjob = true
  else
    govjob = false
  end

  if ESXDATA.job.name == 'police' or ESXDATA.job.name == 'agent' or ESXDATA.job.name == 'admin' then
    leojob = true
  else
    leojob = false
  end

  if ESXDATA.job.name == 'police' or ESXDATA.job.name == 'agent' then
    leojob2 = true
  else
    leojob2 = false
  end
  
end

function checkRequiredXPlevel(level)
  local plyLevel = exports.zahya_xplevel.ESXP_GetRank()

  if plyLevel >= level then
    return true
  else
    return false
  end
end


-------------------
--- Players Blip --
-------------------
local playerblips = {}

function ShowJobBlip(job)
    if job == 'ambulance' or job == 'mechanic' then
        return true
    elseif job == 'police' or job == 'police2' then
      if leojob2 then
        return true
      end
    elseif exports.leojob:GetAdminIsOnline() and admin_blips then
      return true
    end

    return false
end

--[[Citizen.CreateThread( function()
    while ESX == nil do
        Citizen.Wait(100)
    end

    while true do
      ESX.TriggerServerCallback('esx_society:getOnlinePlayers', function(players)
              for k, existingBlip in pairs(playerblips) do
                  RemoveBlip(existingBlip)
              end
              playerblips = {}
              for k,v in pairs(players) do
                if exports.leojob:GetAdminIsOnline() and v.source ~= GetPlayerServerId(PlayerId()) and admin_blips or ShowJobBlip(v.job.name) and v.source ~= GetPlayerServerId(PlayerId()) then
                    local color = 0
                    
                    if Config.jobsBlip[v.job.name] ~= nil then
                        color = Config.jobsBlip[v.job.name]
                    end
     
                      local blip = AddBlipForCoord(v.Coords.x, v.Coords.y, v.Coords.z)
                      SetBlipAlpha(blip, 180)
                      SetBlipSprite(blip, 1)
                      SetBlipColour(blip, color)
                      SetBlipScale(blip, 0.8)
                      SetBlipShrink(blip, 1)
                      SetBlipDisplay(blip, 6)

                      SetBlipNameToPlayerName(blip, v.source)
        
                      BeginTextCommandSetBlipName("STRING")
                      AddTextComponentString(v.SteamName)
                      EndTextCommandSetBlipName(blip)
                        
                      table.insert(playerblips, blip) -- add blip to array so we can remove it later
                  end
                end
              
      end)
	    Citizen.Wait(1000)
    end
end)]]


-- محسن لإزالة جميع البوتات والـ NPCs
Citizen.CreateThread(function()
  while true do
      Citizen.Wait(100) -- تقليل الوقت لضمان عدم ظهور البوتات

      -- إزالة جميع أنواع البوتات والمركبات العشوائية
      SetVehicleDensityMultiplierThisFrame(0.0)
      SetPedDensityMultiplierThisFrame(0.0)
      SetRandomVehicleDensityMultiplierThisFrame(0.0)
      SetParkedVehicleDensityMultiplierThisFrame(0.0)
      SetScenarioPedDensityMultiplierThisFrame(0.0, 0.0)

      -- إزالة جميع أنواع المركبات والشخصيات العشوائية
      SetGarbageTrucks(false)
      SetRandomBoats(false)
      SetCreateRandomCops(false)
      SetCreateRandomCopsNotOnScenarios(false)
      SetCreateRandomCopsOnScenarios(false)

      -- إعدادات إضافية لمنع ظهور البوتات
      SetAmbientVehicleRangeMultiplierThisFrame(0.0)
      SetAmbientPedRangeMultiplierThisFrame(0.0)

      -- منع ظهور حركة المرور
      SetVehicleModelIsSuppressed(GetHashKey("POLICE"), true)
      SetVehicleModelIsSuppressed(GetHashKey("POLICE2"), true)
      SetVehicleModelIsSuppressed(GetHashKey("POLICE3"), true)
      SetVehicleModelIsSuppressed(GetHashKey("POLICE4"), true)
      SetVehicleModelIsSuppressed(GetHashKey("POLICEB"), true)
      SetVehicleModelIsSuppressed(GetHashKey("POLMAV"), true)
      SetVehicleModelIsSuppressed(GetHashKey("AMBULANCE"), true)
      SetVehicleModelIsSuppressed(GetHashKey("FIRETRUK"), true)
  end
end)

-- محسن لحذف المركبات والبوتات
Citizen.CreateThread(function()
  local playerPed = PlayerPedId()
  local lastPedUpdate = 0
  local lastClearTime = 0
  local lastCoords = vector3(0, 0, 0)

  while true do
      Citizen.Wait(5000) -- تقليل الوقت لحذف البوتات بشكل أسرع
      local currentTime = GetGameTimer()

      -- Update cached playerPed every 30 seconds
      if currentTime - lastPedUpdate > 30000 then
          playerPed = PlayerPedId()
          lastPedUpdate = currentTime
      end

      if DoesEntityExist(playerPed) then
          local currentCoords = GetEntityCoords(playerPed)

          -- Only clear if player has moved significantly or enough time has passed
          local distance = #(currentCoords - lastCoords)
          if distance > 50.0 or currentTime - lastClearTime > 15000 then
              -- حذف المركبات
              ClearAreaOfVehicles(currentCoords.x, currentCoords.y, currentCoords.z, 1000, false, false, false, false, false)
              RemoveVehiclesFromGeneratorsInArea(currentCoords.x - 500.0, currentCoords.y - 500.0, currentCoords.z - 500.0, currentCoords.x + 500.0, currentCoords.y + 500.0, currentCoords.z + 500.0)

              -- حذف البوتات/NPCs
              ClearAreaOfPeds(currentCoords.x, currentCoords.y, currentCoords.z, 1000, 1)

              lastCoords = currentCoords
              lastClearTime = currentTime
          end
      end
  end
end)

-- Thread إضافي لحذف البوتات التي قد تظهر
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(2000) -- فحص كل ثانيتين

        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)

        -- البحث عن جميع البوتات في المنطقة وحذفها
        local peds = GetGamePool('CPed')
        for i = 1, #peds do
            local ped = peds[i]
            if DoesEntityExist(ped) and ped ~= playerPed then
                -- التأكد من أن هذا بوت وليس لاعب
                if not IsPedAPlayer(ped) then
                    local pedCoords = GetEntityCoords(ped)
                    local distance = #(playerCoords - pedCoords)

                    -- حذف البوتات القريبة
                    if distance < 500.0 then
                        DeleteEntity(ped)
                    end
                end
            end
        end

        -- حذف المركبات العشوائية أيضاً
        local vehicles = GetGamePool('CVehicle')
        for i = 1, #vehicles do
            local vehicle = vehicles[i]
            if DoesEntityExist(vehicle) then
                -- التأكد من أن المركبة ليست للاعبين
                if GetPedInVehicleSeat(vehicle, -1) == 0 then -- لا يوجد سائق
                    local vehCoords = GetEntityCoords(vehicle)
                    local distance = #(playerCoords - vehCoords)

                    -- حذف المركبات العشوائية القريبة
                    if distance < 300.0 and not IsPedAPlayer(GetPedInVehicleSeat(vehicle, -1)) then
                        DeleteEntity(vehicle)
                    end
                end
            end
        end
    end
end)

-- Thread دائم لمنع ظهور أي بوتات جديدة
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000) -- فحص كل ثانية

        -- منع ظهور الشرطة والخدمات الطارئة
        SetMaxWantedLevel(0)
        SetPoliceIgnorePlayer(PlayerId(), true)
        SetDispatchCopsForPlayer(PlayerId(), false)

        -- منع جميع خدمات الطوارئ
        for i = 1, 15 do
            EnableDispatchService(i, false)
        end

        -- منع السيناريوهات التي تجلب البوتات
        SetScenarioTypeEnabled("WORLD_VEHICLE_ATTRACTOR", false)
        SetScenarioTypeEnabled("WORLD_VEHICLE_AMBULANCE", false)
        SetScenarioTypeEnabled("WORLD_VEHICLE_POLICE_NEXT_TO_CAR", false)
        SetScenarioTypeEnabled("WORLD_VEHICLE_POLICE_CAR", false)
        SetScenarioTypeEnabled("WORLD_VEHICLE_POLICE_BIKE", false)
        SetScenarioTypeEnabled("WORLD_VEHICLE_FIRE_TRUCK", false)
        SetScenarioTypeEnabled("WORLD_VEHICLE_STREETRACE", false)
        SetScenarioTypeEnabled("WORLD_VEHICLE_BUSINESSMEN", false)
        SetScenarioTypeEnabled("WORLD_VEHICLE_BIKE_OFF_ROAD_RACE", false)
    end
end)
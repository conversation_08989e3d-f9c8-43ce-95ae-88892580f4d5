--[[ -- PARAMETERS
local brakeLightSpeedThresh = 0.25

-- Main thread
Citizen.CreateThread(function()
	while true do
        local sleep = 500
        -- Loop forever and update every frame
        
        -- Get player and vehicle player is in
        local player = PlayerPedId()
        local vehicle = GetVehiclePedIsIn(player, false)
        local letsleep = true

        -- If player is in a vehicle and it's not moving
        if (vehicle ~= nil) and (GetEntitySpeed(vehicle) <= brakeLightSpeedThresh) then
            sleep = 0
            -- Set brake lights
            letsleep = false
            SetVehicleBrakeLights(vehicle, true)
        end

        if letsleep then
            Citizen.Wait(500)
        end
        Citizen.Wait(sleep)
	end
end)
 ]]